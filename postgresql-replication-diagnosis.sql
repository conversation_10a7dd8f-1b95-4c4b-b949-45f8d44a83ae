-- PostgreSQL Replication Diagnosis Script for TempFly.io
-- Run this script on each server (EU, US, SG) to diagnose replication issues
-- Usage: psql -d tempfly_app -f postgresql-replication-diagnosis.sql

\echo '=== POSTGRESQL REPLICATION DIAGNOSIS FOR TEMPFLY.IO ==='
\echo ''

-- Server identification
\echo '1. SERVER IDENTIFICATION:'
SELECT 
    'Server Info' as check_type,
    inet_server_addr() as server_ip,
    current_database() as database_name,
    version() as postgresql_version;

\echo ''
\echo '2. PUBLICATION STATUS:'
-- Check publications
SELECT 
    pubname as publication_name,
    puballtables as all_tables,
    pubinsert as allows_insert,
    pubupdate as allows_update,
    pubdelete as allows_delete,
    pubtruncate as allows_truncate
FROM pg_publication;

\echo ''
\echo '3. PUBLISHED TABLES:'
-- Check which tables are published
SELECT 
    pub.pubname as publication_name,
    pt.schemaname,
    pt.tablename
FROM pg_publication pub
JOIN pg_publication_tables pt ON pub.pubname = pt.pubname
ORDER BY pub.pubname, pt.tablename;

\echo ''
\echo '4. SUBSCRIPTION STATUS:'
-- Check subscriptions
SELECT 
    subname as subscription_name,
    subenabled as enabled,
    subconninfo as connection_info
FROM pg_subscription;

\echo ''
\echo '5. SUBSCRIPTION WORKER STATUS:'
-- Check subscription worker processes
SELECT
    subname as subscription_name,
    pid as worker_pid,
    relid,
    received_lsn,
    latest_end_lsn,
    latest_end_time,
    CASE
        WHEN pid IS NULL THEN '❌ INACTIVE'
        ELSE '✅ ACTIVE'
    END as status,
    CASE 
        WHEN latest_end_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - latest_end_time))::INTEGER || ' seconds ago'
        ELSE 'Never'
    END as last_activity
FROM pg_stat_subscription;

\echo ''
\echo '6. REPLICATION SLOTS:'
-- Check replication slots
SELECT
    slot_name,
    plugin,
    slot_type,
    database,
    active,
    restart_lsn,
    confirmed_flush_lsn,
    CASE
        WHEN active THEN '✅ ACTIVE'
        ELSE '❌ INACTIVE'
    END as status
FROM pg_replication_slots;

\echo ''
\echo '7. WAL SENDER PROCESSES:'
-- Check WAL sender processes (outgoing replication)
SELECT
    application_name,
    client_addr,
    client_hostname,
    client_port,
    backend_start,
    state,
    sent_lsn,
    write_lsn,
    flush_lsn,
    replay_lsn,
    write_lag,
    flush_lag,
    replay_lag,
    sync_state
FROM pg_stat_replication;

\echo ''
\echo '8. INBOX DATA DISTRIBUTION:'
-- Check inbox distribution (the actual problem)
SELECT 
    'Total Inboxes' as metric,
    COUNT(*) as count,
    COUNT(*) FILTER (WHERE is_active = true) as active_count
FROM inboxes
UNION ALL
SELECT 
    'Recent Inboxes (24h)' as metric,
    COUNT(*) as count,
    COUNT(*) FILTER (WHERE is_active = true) as active_count
FROM inboxes 
WHERE created_at > NOW() - INTERVAL '24 hours';

\echo ''
\echo '9. INBOX CREATION TIMELINE:'
-- Show recent inbox creation timeline to identify replication patterns
SELECT 
    id,
    name,
    domain,
    address,
    created_at,
    is_active,
    CASE 
        WHEN rapidapi_key IS NOT NULL THEN 'RapidAPI'
        WHEN api_key_id IS NOT NULL THEN 'API Key'
        ELSE 'Unknown'
    END as auth_type
FROM inboxes 
WHERE created_at > NOW() - INTERVAL '7 days'
ORDER BY created_at DESC
LIMIT 20;

\echo ''
\echo '10. REPLICATION ERRORS (if any):'
-- Check for replication-related errors in pg_stat_database_conflicts
SELECT 
    datname,
    confl_tablespace,
    confl_lock,
    confl_snapshot,
    confl_bufferpin,
    confl_deadlock
FROM pg_stat_database_conflicts 
WHERE datname = 'tempfly_app';

\echo ''
\echo '11. SUBSCRIPTION RELATION STATUS:'
-- Check subscription relation status
SELECT 
    sr.srsubid,
    s.subname,
    sr.srrelid,
    c.relname as table_name,
    sr.srsubstate as sync_state,
    CASE sr.srsubstate
        WHEN 'i' THEN 'Initialize'
        WHEN 'd' THEN 'Data copy'
        WHEN 's' THEN 'Synchronized'
        WHEN 'r' THEN 'Ready'
        ELSE 'Unknown: ' || sr.srsubstate::text
    END as sync_state_description
FROM pg_subscription_rel sr
JOIN pg_subscription s ON sr.srsubid = s.oid
JOIN pg_class c ON sr.srrelid = c.oid;

\echo ''
\echo '12. CURRENT LSN POSITIONS:'
-- Check current LSN positions
SELECT 
    'Current WAL LSN' as metric,
    pg_current_wal_lsn() as lsn_position;

\echo ''
\echo '=== DIAGNOSIS COMPLETE ==='
\echo 'Save this output and compare across all three servers (EU, US, SG)'
\echo 'Look for differences in subscription status, worker processes, and data counts'
