-- PostgreSQL Replication Test Script for TempFly.io
-- This script tests bidirectional replication after applying fixes
-- Run this script on each server to verify replication is working correctly

\echo '=== TEMPFLY.IO REPLICATION TEST SCRIPT ==='
\echo ''

-- Test 1: Check current replication status
\echo 'TEST 1: REPLICATION STATUS CHECK'
\echo '==============================='

SELECT 
    'Server Info' as test_type,
    inet_server_addr() as server_ip,
    CASE 
        WHEN inet_server_addr()::text = '************' THEN 'EU Server'
        WHEN inet_server_addr()::text = '*************' THEN 'US Server'  
        WHEN inet_server_addr()::text = '*************' THEN 'SG Server'
        ELSE 'Unknown Server (' || inet_server_addr()::text || ')'
    END as server_region;

-- Check subscription status
SELECT 
    'Subscription Status' as test_type,
    subname,
    subenabled as enabled,
    CASE 
        WHEN subenabled THEN '✅ Enabled'
        ELSE '❌ Disabled'
    END as status
FROM pg_subscription
ORDER BY subname;

-- Check worker processes
SELECT 
    'Worker Status' as test_type,
    subname,
    CASE
        WHEN pid IS NULL THEN '❌ INACTIVE'
        ELSE '✅ ACTIVE'
    END as worker_status,
    CASE 
        WHEN latest_end_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - latest_end_time))::INTEGER || ' seconds ago'
        ELSE 'Never'
    END as last_activity
FROM pg_stat_subscription
ORDER BY subname;

-- Test 2: Current data state
\echo ''
\echo 'TEST 2: CURRENT DATA STATE'
\echo '========================='

SELECT 
    'Current Data Count' as test_type,
    COUNT(*) as total_inboxes,
    COUNT(*) FILTER (WHERE is_active = true) as active_inboxes,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 hour') as recent_inboxes,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') as today_inboxes
FROM inboxes;

-- Show recent inboxes to verify replication
SELECT 
    'Recent Inboxes' as test_type,
    name,
    domain,
    address,
    created_at,
    CASE 
        WHEN rapidapi_key IS NOT NULL THEN 'RapidAPI'
        WHEN api_key_id IS NOT NULL THEN 'API Key'
        ELSE 'Unknown'
    END as auth_type
FROM inboxes 
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC
LIMIT 10;

-- Test 3: Create test inbox for replication verification
\echo ''
\echo 'TEST 3: CREATING TEST INBOX FOR REPLICATION'
\echo '=========================================='

-- Create a test inbox with server identifier
DO $$
DECLARE
    server_region TEXT;
    test_name TEXT;
    test_domain TEXT := 'boxqix.com';
    test_inbox_id UUID;
BEGIN
    -- Determine server region
    SELECT CASE 
        WHEN inet_server_addr()::text = '************' THEN 'EU'
        WHEN inet_server_addr()::text = '*************' THEN 'US'  
        WHEN inet_server_addr()::text = '*************' THEN 'SG'
        ELSE 'UNKNOWN'
    END INTO server_region;
    
    -- Create unique test name with timestamp
    test_name := 'test-repl-' || server_region || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
    
    -- Insert test inbox
    INSERT INTO inboxes (name, domain, address, email, created_at, is_active)
    VALUES (
        test_name,
        test_domain,
        test_name || '@' || test_domain,
        test_name || '@' || test_domain,
        NOW(),
        true
    ) RETURNING id INTO test_inbox_id;
    
    RAISE NOTICE 'Created test inbox: % (ID: %)', test_name, test_inbox_id;
    RAISE NOTICE 'This inbox should appear on all servers within 30 seconds';
    
EXCEPTION
    WHEN unique_violation THEN
        RAISE NOTICE 'Test inbox already exists (this is normal if running multiple times)';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating test inbox: %', SQLERRM;
END $$;

-- Test 4: Check replication lag
\echo ''
\echo 'TEST 4: REPLICATION LAG CHECK'
\echo '============================'

SELECT 
    'Replication Lag' as test_type,
    subname,
    CASE 
        WHEN latest_end_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - latest_end_time)) || ' seconds'
        ELSE 'No data'
    END as lag_seconds,
    CASE 
        WHEN latest_end_time IS NULL THEN '❌ No Activity'
        WHEN EXTRACT(EPOCH FROM (NOW() - latest_end_time)) < 30 THEN '✅ Low Lag'
        WHEN EXTRACT(EPOCH FROM (NOW() - latest_end_time)) < 300 THEN '⚠️ Medium Lag'
        ELSE '❌ High Lag'
    END as lag_status
FROM pg_stat_subscription
ORDER BY subname;

-- Test 5: Check for replication errors
\echo ''
\echo 'TEST 5: REPLICATION ERROR CHECK'
\echo '=============================='

-- Check for conflicts
SELECT 
    'Database Conflicts' as test_type,
    datname,
    confl_tablespace + confl_lock + confl_snapshot + confl_bufferpin + confl_deadlock as total_conflicts
FROM pg_stat_database_conflicts 
WHERE datname = 'tempfly_app';

-- Check subscription relation sync state
SELECT 
    'Sync State' as test_type,
    s.subname,
    c.relname as table_name,
    CASE sr.srsubstate
        WHEN 'i' THEN 'Initialize'
        WHEN 'd' THEN 'Data copy'
        WHEN 's' THEN 'Synchronized ✅'
        WHEN 'r' THEN 'Ready ✅'
        ELSE 'Unknown: ' || sr.srsubstate
    END as sync_state
FROM pg_subscription_rel sr
JOIN pg_subscription s ON sr.srsubid = s.oid
JOIN pg_class c ON sr.srrelid = c.oid
WHERE c.relname IN ('inboxes', 'emails', 'domains', 'api_keys', 'attachments')
ORDER BY s.subname, c.relname;

-- Test 6: Final verification
\echo ''
\echo 'TEST 6: FINAL VERIFICATION'
\echo '========================='

\echo 'Wait 30 seconds, then run this query on ALL servers:'
\echo 'The inbox counts should be identical across all regions.'
\echo ''
\echo 'SELECT COUNT(*) as total_inboxes FROM inboxes WHERE is_active = true;'
\echo ''
\echo 'If counts differ, replication is not working properly.'
\echo ''
\echo 'To test real-time replication:'
\echo '1. Create an inbox via API on one server'
\echo '2. Check if it appears on other servers within 30 seconds'
\echo '3. All servers should return the same inbox list'
\echo ''
\echo '=== REPLICATION TEST COMPLETE ==='
\echo ''
\echo 'EXPECTED RESULTS:'
\echo '✅ All subscriptions should be enabled and active'
\echo '✅ All worker processes should be running (pid not null)'
\echo '✅ Replication lag should be < 30 seconds'
\echo '✅ All tables should be in "Synchronized" or "Ready" state'
\echo '✅ Inbox counts should be identical across all servers'
\echo ''
\echo 'If any test fails, check PostgreSQL logs:'
\echo 'sudo tail -f /var/log/postgresql/postgresql-16-main.log'
