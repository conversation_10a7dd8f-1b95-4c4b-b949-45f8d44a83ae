-- PostgreSQL Replication Complete Reset Script for TempFly.io
-- Use this script ONLY if the basic fix script doesn't resolve the issue
-- This script completely resets replication and re-establishes it properly
--
-- WARNING: This will temporarily stop replication and may cause brief data inconsistency
-- Only run during maintenance window or low-traffic periods
--
-- SEQUENCE TO RUN:
-- 1. Run on SG server first (has complete data)
-- 2. Run on US server second  
-- 3. Run on EU server last

\echo '=== TEMPFLY.IO REPLICATION COMPLETE RESET ==='
\echo ''
\echo 'WARNING: This script completely resets replication!'
\echo 'Only proceed if the basic fix script did not work.'
\echo ''
\echo 'Current server data before reset:'

-- Show current data state
SELECT 
    'Pre-Reset Data Count' as metric,
    COUNT(*) as total_inboxes,
    COUNT(*) FILTER (WHERE is_active = true) as active_inboxes,
    MIN(created_at) as oldest_inbox,
    MAX(created_at) as newest_inbox
FROM inboxes;

\echo ''
\echo 'STEP 1: DROPPING ALL SUBSCRIPTIONS'
\echo '================================='

-- Drop all subscriptions completely
DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        BEGIN
            EXECUTE format('DROP SUBSCRIPTION %I', sub_record.subname);
            RAISE NOTICE 'Dropped subscription: %', sub_record.subname;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error dropping subscription %: %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

\echo ''
\echo 'STEP 2: CLEANING UP REPLICATION SLOTS'
\echo '===================================='

-- Clean up all replication slots
DO $$
DECLARE
    slot_record RECORD;
BEGIN
    FOR slot_record IN SELECT slot_name FROM pg_replication_slots LOOP
        BEGIN
            PERFORM pg_drop_replication_slot(slot_record.slot_name);
            RAISE NOTICE 'Dropped replication slot: %', slot_record.slot_name;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error dropping slot %: %', slot_record.slot_name, SQLERRM;
        END;
    END LOOP;
END $$;

\echo ''
\echo 'STEP 3: VERIFYING PUBLICATIONS EXIST'
\echo '=================================='

-- Ensure publications exist (don't drop them, just verify)
SELECT 
    'Publication Check' as check_type,
    pubname,
    puballtables,
    pubinsert,
    pubupdate,
    pubdelete
FROM pg_publication;

-- Show published tables
SELECT 
    'Published Tables' as check_type,
    pub.pubname,
    pt.tablename
FROM pg_publication pub
JOIN pg_publication_tables pt ON pub.pubname = pt.pubname
ORDER BY pub.pubname, pt.tablename;

\echo ''
\echo 'STEP 4: WAITING FOR CLEANUP TO COMPLETE'
\echo '======================================'

SELECT pg_sleep(3);

\echo ''
\echo 'STEP 5: CHECKING CURRENT SERVER IP'
\echo '================================='

-- Identify which server this is
SELECT 
    'Server Identification' as info_type,
    inet_server_addr() as server_ip,
    CASE 
        WHEN inet_server_addr()::text = '************' THEN 'EU Server'
        WHEN inet_server_addr()::text = '*************' THEN 'US Server'  
        WHEN inet_server_addr()::text = '*************' THEN 'SG Server'
        ELSE 'Unknown Server'
    END as server_region;

\echo ''
\echo 'STEP 6: READY FOR SUBSCRIPTION RECREATION'
\echo '========================================'

\echo 'Replication reset complete. Now manually create subscriptions in this order:'
\echo ''
\echo 'FOR EU SERVER (************):'
\echo '------------------------------'
\echo 'CREATE SUBSCRIPTION eu_from_us_subscription'
\echo 'CONNECTION ''host=************* port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'''
\echo 'PUBLICATION us_publication'
\echo 'WITH (copy_data = false, create_slot = true, enabled = true);'
\echo ''
\echo 'CREATE SUBSCRIPTION eu_from_sg_subscription'
\echo 'CONNECTION ''host=************* port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'''
\echo 'PUBLICATION sg_publication'
\echo 'WITH (copy_data = false, create_slot = true, enabled = true);'
\echo ''
\echo 'FOR US SERVER (*************):'
\echo '-------------------------------'
\echo 'CREATE SUBSCRIPTION us_from_eu_subscription'
\echo 'CONNECTION ''host=************ port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'''
\echo 'PUBLICATION eu_publication'
\echo 'WITH (copy_data = false, create_slot = true, enabled = true);'
\echo ''
\echo 'CREATE SUBSCRIPTION us_from_sg_subscription'
\echo 'CONNECTION ''host=************* port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'''
\echo 'PUBLICATION sg_publication'
\echo 'WITH (copy_data = false, create_slot = true, enabled = true);'
\echo ''
\echo 'FOR SG SERVER (*************):'
\echo '-------------------------------'
\echo 'CREATE SUBSCRIPTION sg_from_eu_subscription'
\echo 'CONNECTION ''host=************ port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'''
\echo 'PUBLICATION eu_publication'
\echo 'WITH (copy_data = false, create_slot = true, enabled = true);'
\echo ''
\echo 'CREATE SUBSCRIPTION sg_from_us_subscription'
\echo 'CONNECTION ''host=************* port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'''
\echo 'PUBLICATION us_publication'
\echo 'WITH (copy_data = false, create_slot = true, enabled = true);'
\echo ''
\echo 'IMPORTANT NOTES:'
\echo '- Use copy_data = false to prevent duplicate key conflicts'
\echo '- SG server should have the complete dataset already'
\echo '- After creating subscriptions, wait 5-10 minutes for sync'
\echo '- Test by creating a new inbox on each server'
\echo ''
\echo '=== RESET COMPLETE - MANUAL SUBSCRIPTION CREATION REQUIRED ==='
