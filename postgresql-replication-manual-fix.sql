-- Manual PostgreSQL Replication Fix for TempFly.io
-- Based on diagnosis results showing specific subscription issues
-- Run this script step by step on each server

\echo '=== MANUAL REPLICATION FIX FOR TEMPFLY.IO ==='
\echo ''

-- Step 1: Check current status
\echo 'STEP 1: CURRENT STATUS'
\echo '===================='

SELECT 'Current subscriptions:' as info;
SELECT subname, subenabled FROM pg_subscription;

SELECT 'Active workers:' as info;
SELECT subname, pid IS NOT NULL as has_worker FROM pg_stat_subscription;

SELECT 'Inactive slots:' as info;
SELECT slot_name FROM pg_replication_slots WHERE NOT active;

-- Step 2: Clean up inactive slots
\echo ''
\echo 'STEP 2: CLEANING INACTIVE SLOTS'
\echo '=============================='

-- Drop inactive slots on SG server
DROP REPLICATION SLOT IF EXISTS eu_from_sg_subscription;
DROP REPLICATION SLOT IF EXISTS us_from_sg_subscription;

\echo 'Inactive slots cleaned up'

-- Step 3: Server-specific fixes
\echo ''
\echo 'STEP 3: SERVER-SPECIFIC FIXES'
\echo '============================'

-- Check which server this is by looking at existing subscriptions
DO $$
DECLARE
    is_sg_server BOOLEAN := FALSE;
    is_eu_server BOOLEAN := FALSE;
    is_us_server BOOLEAN := FALSE;
BEGIN
    -- Determine server type by subscription patterns
    SELECT EXISTS(SELECT 1 FROM pg_subscription WHERE subname = 'sg_from_us_subscription') INTO is_sg_server;
    SELECT EXISTS(SELECT 1 FROM pg_subscription WHERE subname LIKE 'eu_from_%') INTO is_eu_server;
    SELECT EXISTS(SELECT 1 FROM pg_subscription WHERE subname LIKE 'us_from_%') INTO is_us_server;
    
    IF is_sg_server THEN
        RAISE NOTICE 'Detected SG Server - creating missing subscription to EU';
        
        -- Create missing subscription to EU server if it doesn't exist
        IF NOT EXISTS(SELECT 1 FROM pg_subscription WHERE subname = 'sg_from_eu_subscription') THEN
            RAISE NOTICE 'Creating sg_from_eu_subscription...';
            -- This will be done manually outside this block
        ELSE
            RAISE NOTICE 'sg_from_eu_subscription already exists';
        END IF;
        
    ELSIF is_eu_server THEN
        RAISE NOTICE 'Detected EU Server - checking subscriptions';
        
    ELSIF is_us_server THEN
        RAISE NOTICE 'Detected US Server - checking subscriptions';
        
    ELSE
        RAISE NOTICE 'Could not determine server type';
    END IF;
END $$;

-- Step 4: Manual subscription creation (run only on SG server)
\echo ''
\echo 'STEP 4: MANUAL SUBSCRIPTION CREATION'
\echo '=================================='

\echo 'If this is the SG server and sg_from_eu_subscription does not exist, run:'
\echo 'CREATE SUBSCRIPTION sg_from_eu_subscription'
\echo 'CONNECTION '\''host=************ port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'\''
\echo 'PUBLICATION eu_publication'
\echo 'WITH (copy_data = false, create_slot = true, enabled = true);'

-- Step 5: Refresh existing subscriptions
\echo ''
\echo 'STEP 5: REFRESHING SUBSCRIPTIONS'
\echo '=============================='

-- Refresh subscriptions one by one (outside transaction blocks)
\echo 'Refreshing subscriptions individually...'

-- This needs to be done manually for each subscription
\echo 'Run these commands manually:'
\echo 'ALTER SUBSCRIPTION sg_from_us_subscription REFRESH PUBLICATION;'
\echo 'ALTER SUBSCRIPTION eu_from_us_subscription REFRESH PUBLICATION;'
\echo 'ALTER SUBSCRIPTION eu_from_sg_subscription REFRESH PUBLICATION;'
\echo 'ALTER SUBSCRIPTION us_from_eu_subscription REFRESH PUBLICATION;'
\echo 'ALTER SUBSCRIPTION us_from_sg_subscription REFRESH PUBLICATION;'
\echo 'ALTER SUBSCRIPTION sg_from_eu_subscription REFRESH PUBLICATION;'

-- Step 6: Final verification
\echo ''
\echo 'STEP 6: VERIFICATION'
\echo '=================='

SELECT 'Final subscription status:' as info;
SELECT 
    subname,
    subenabled as enabled,
    CASE WHEN subenabled THEN 'Enabled' ELSE 'Disabled' END as status
FROM pg_subscription 
ORDER BY subname;

SELECT 'Final worker status:' as info;
SELECT 
    subname,
    CASE WHEN pid IS NOT NULL THEN 'Active' ELSE 'Inactive' END as worker_status
FROM pg_stat_subscription 
ORDER BY subname;

SELECT 'Current inbox count:' as info;
SELECT COUNT(*) as total_inboxes FROM inboxes WHERE is_active = true;

\echo ''
\echo '=== MANUAL FIX COMPLETE ==='
\echo ''
\echo 'NEXT STEPS:'
\echo '1. If on SG server, create the missing sg_from_eu_subscription manually'
\echo '2. Refresh all subscriptions using the ALTER SUBSCRIPTION commands above'
\echo '3. Wait 5 minutes for synchronization'
\echo '4. Check that all servers show the same inbox count'
\echo '5. Test creating a new inbox to verify bidirectional replication'
\echo ''
\echo 'EXPECTED RESULT: All servers should show 3 inboxes (eu, usa, sg)'
