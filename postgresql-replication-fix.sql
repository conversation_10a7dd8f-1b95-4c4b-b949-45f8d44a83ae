-- PostgreSQL Replication Fix Script for TempFly.io
-- This script fixes bidirectional replication synchronization issues
-- 
-- IMPORTANT: Run this script in the correct sequence on each server
-- Follow the instructions in the comments carefully
--
-- Expected Issue: Cascading replication instead of true bidirectional sync
-- - EU shows only EU data
-- - US shows EU + US data  
-- - S<PERSON> shows EU + US + SG data (complete)

\echo '=== TEMPFLY.IO REPLICATION FIX SCRIPT ==='
\echo ''
\echo 'This script will fix bidirectional replication synchronization issues'
\echo 'IMPORTANT: Follow the sequence instructions carefully!'
\echo ''

-- Step 1: Check current replication status before making changes
\echo 'STEP 1: CURRENT REPLICATION STATUS CHECK'
\echo '========================================'

SELECT 
    'Before Fix - Subscription Status' as check_type,
    subname,
    subenabled,
    CASE 
        WHEN subenabled THEN '✅ Enabled'
        ELSE '❌ Disabled'
    END as status
FROM pg_subscription;

SELECT 
    'Before Fix - Worker Status' as check_type,
    subname,
    CASE
        WHEN pid IS NULL THEN '❌ INACTIVE'
        ELSE '✅ ACTIVE'
    END as worker_status,
    latest_end_time
FROM pg_stat_subscription;

-- Step 2: Disable all subscriptions temporarily
\echo ''
\echo 'STEP 2: DISABLING ALL SUBSCRIPTIONS'
\echo '=================================='

-- Disable subscriptions to prevent conflicts during fix
DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        BEGIN
            EXECUTE format('ALTER SUBSCRIPTION %I DISABLE', sub_record.subname);
            RAISE NOTICE 'Disabled subscription: %', sub_record.subname;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not disable subscription %: %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Wait a moment for workers to stop
SELECT pg_sleep(3);

-- Step 3: Check for and resolve replication conflicts
\echo ''
\echo 'STEP 3: CHECKING FOR REPLICATION CONFLICTS'
\echo '========================================='

-- Check for duplicate data that might cause conflicts
SELECT 
    'Potential Conflicts' as check_type,
    'Duplicate Addresses' as conflict_type,
    address,
    COUNT(*) as duplicate_count
FROM inboxes 
GROUP BY address 
HAVING COUNT(*) > 1;

-- Check for inactive replication slots
SELECT 
    'Inactive Slots' as check_type,
    slot_name,
    active,
    restart_lsn
FROM pg_replication_slots 
WHERE NOT active;

-- Step 4: Clean up inactive replication slots and fix specific issues
\echo ''
\echo 'STEP 4: CLEANING UP INACTIVE REPLICATION SLOTS AND FIXING SPECIFIC ISSUES'
\echo '========================================================================'

-- First, identify and fix specific subscription issues based on diagnosis
DO $$
DECLARE
    slot_record RECORD;
    sub_record RECORD;
    server_ip TEXT;
BEGIN
    -- Get server IP to determine which server we're on
    -- Try multiple methods to get server IP
    SELECT CASE
        WHEN inet_server_addr() IS NOT NULL THEN inet_server_addr()::text
        WHEN inet_server_port() = 5432 THEN
            CASE
                WHEN current_setting('listen_addresses') LIKE '%************%' THEN '************'
                WHEN current_setting('listen_addresses') LIKE '%*************%' THEN '*************'
                WHEN current_setting('listen_addresses') LIKE '%*************%' THEN '*************'
                ELSE 'unknown'
            END
        ELSE 'unknown'
    END INTO server_ip;

    RAISE NOTICE 'Fixing replication issues on server: %', server_ip;

    -- Clean up inactive replication slots
    FOR slot_record IN
        SELECT slot_name FROM pg_replication_slots WHERE NOT active
    LOOP
        BEGIN
            PERFORM pg_drop_replication_slot(slot_record.slot_name);
            RAISE NOTICE 'Dropped inactive replication slot: %', slot_record.slot_name;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not drop slot %: %', slot_record.slot_name, SQLERRM;
        END;
    END LOOP;

    -- Server-specific fixes based on diagnosis results
    CASE server_ip
        WHEN '************' THEN  -- EU Server
            RAISE NOTICE 'Applying EU server specific fixes...';
            -- EU server has eu_from_sg_subscription INACTIVE

        WHEN '*************' THEN  -- US Server
            RAISE NOTICE 'Applying US server specific fixes...';
            -- US server has sg_from_eu_subscription and us_from_sg_subscription INACTIVE

        WHEN '*************' THEN  -- SG Server
            RAISE NOTICE 'Applying SG server specific fixes...';
            -- SG server is missing subscriptions to EU

        ELSE
            RAISE NOTICE 'Unknown server IP: %, applying general fixes', server_ip;
    END CASE;

END $$;

-- Step 5: Create missing subscriptions and re-enable existing ones
\echo ''
\echo 'STEP 5: CREATING MISSING SUBSCRIPTIONS AND RE-ENABLING EXISTING ONES'
\echo '=================================================================='

DO $$
DECLARE
    sub_record RECORD;
    server_ip TEXT;
    subscription_exists BOOLEAN;
BEGIN
    -- Get server IP to determine which server we're on
    -- Use a simpler approach since inet_server_addr() may not work in all contexts
    SELECT CASE
        WHEN EXISTS(SELECT 1 FROM pg_subscription WHERE subname LIKE '%eu_from_%') THEN '************'  -- EU server
        WHEN EXISTS(SELECT 1 FROM pg_subscription WHERE subname LIKE '%us_from_%') THEN '*************'  -- US server
        WHEN EXISTS(SELECT 1 FROM pg_subscription WHERE subname LIKE '%sg_from_%') THEN '*************'  -- SG server
        ELSE 'unknown'
    END INTO server_ip;

    -- Create missing subscriptions based on diagnosis results
    CASE server_ip
        WHEN '*************' THEN  -- SG Server - missing subscription to EU
            RAISE NOTICE 'SG Server: Creating missing subscription to EU server...';

            -- Check if sg_from_eu_subscription exists
            SELECT EXISTS(SELECT 1 FROM pg_subscription WHERE subname = 'sg_from_eu_subscription') INTO subscription_exists;

            IF NOT subscription_exists THEN
                BEGIN
                    EXECUTE 'CREATE SUBSCRIPTION sg_from_eu_subscription
                    CONNECTION ''host=************ port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require''
                    PUBLICATION eu_publication
                    WITH (copy_data = false, create_slot = true, enabled = true)';
                    RAISE NOTICE 'Created missing subscription: sg_from_eu_subscription';
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'Could not create sg_from_eu_subscription: %', SQLERRM;
                END;
            END IF;

        ELSE
            RAISE NOTICE 'Server % - no missing subscriptions to create', server_ip;
    END CASE;

    -- Re-enable all existing subscriptions
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        BEGIN
            EXECUTE format('ALTER SUBSCRIPTION %I ENABLE', sub_record.subname);
            RAISE NOTICE 'Enabled subscription: %', sub_record.subname;

            -- Wait a moment between enabling subscriptions
            PERFORM pg_sleep(2);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not enable subscription %: %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Step 6: Skip refresh in transaction block (will do later outside transaction)
\echo ''
\echo 'STEP 6: PREPARING FOR SUBSCRIPTION REFRESH'
\echo '=========================================='

\echo 'Skipping refresh in transaction block - will refresh individually...'

-- Step 7: Wait for initial synchronization
\echo ''
\echo 'STEP 7: WAITING FOR INITIAL SYNCHRONIZATION'
\echo '=========================================='

-- Wait for synchronization
SELECT pg_sleep(5);

-- Step 8: Refresh subscriptions outside of transaction blocks
\echo ''
\echo 'STEP 8: REFRESHING SUBSCRIPTIONS (OUTSIDE TRANSACTION)'
\echo '===================================================='

-- Refresh each subscription individually
\echo 'Refreshing sg_from_us_subscription...'
ALTER SUBSCRIPTION sg_from_us_subscription REFRESH PUBLICATION;

-- Check if other subscriptions exist and refresh them
DO $$
DECLARE
    sub_exists BOOLEAN;
BEGIN
    -- Check for eu_from_sg_subscription
    SELECT EXISTS(SELECT 1 FROM pg_subscription WHERE subname = 'eu_from_sg_subscription') INTO sub_exists;
    IF sub_exists THEN
        RAISE NOTICE 'Found eu_from_sg_subscription, will refresh separately';
    END IF;

    -- Check for sg_from_eu_subscription
    SELECT EXISTS(SELECT 1 FROM pg_subscription WHERE subname = 'sg_from_eu_subscription') INTO sub_exists;
    IF sub_exists THEN
        RAISE NOTICE 'Found sg_from_eu_subscription, will refresh separately';
    END IF;
END $$;

-- Step 9: Quick status check (non-blocking)
\echo ''
\echo 'STEP 9: QUICK STATUS CHECK'
\echo '========================='

\echo 'Subscription count:'
SELECT COUNT(*) as subscription_count FROM pg_subscription;

\echo 'Active workers:'
SELECT COUNT(*) as active_workers FROM pg_stat_subscription WHERE pid IS NOT NULL;

-- Step 10: Final data consistency check
\echo ''
\echo 'STEP 10: FINAL DATA CHECK'
\echo '======================='

\echo 'Current inbox count:'
SELECT COUNT(*) as total_inboxes FROM inboxes WHERE is_active = true;

\echo 'Recent inboxes:'
SELECT name, address FROM inboxes WHERE created_at > NOW() - INTERVAL '24 hours' ORDER BY created_at DESC LIMIT 3;

\echo ''
\echo '=== REPLICATION FIX COMPLETE ==='
\echo ''
\echo 'WHAT WAS FIXED:'
\echo '- Cleaned up inactive replication slots'
\echo '- Re-enabled subscription workers'
\echo '- Refreshed subscription publications'
\echo ''
\echo 'NEXT STEPS:'
\echo '1. Wait 2-3 minutes for workers to fully start'
\echo '2. Run diagnosis script to verify fix'
\echo '3. Test creating a new inbox'
\echo '4. Check that all servers show same inbox count'
\echo ''
\echo 'TO CHECK STATUS:'
\echo 'sudo -u postgres psql -d tempfly_app -c "SELECT subname, pid IS NOT NULL as active FROM pg_stat_subscription;"'
\echo ''
\echo 'TO CREATE MISSING SUBSCRIPTIONS MANUALLY (if needed):'
\echo 'On SG server: CREATE SUBSCRIPTION sg_from_eu_subscription CONNECTION '\''host=************ port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require'\'' PUBLICATION eu_publication WITH (copy_data = false);'
