-- PostgreSQL Replication Fix Script for TempFly.io
-- This script fixes bidirectional replication synchronization issues
-- 
-- IMPORTANT: Run this script in the correct sequence on each server
-- Follow the instructions in the comments carefully
--
-- Expected Issue: Cascading replication instead of true bidirectional sync
-- - EU shows only EU data
-- - US shows EU + US data  
-- - S<PERSON> shows EU + US + SG data (complete)

\echo '=== TEMPFLY.IO REPLICATION FIX SCRIPT ==='
\echo ''
\echo 'This script will fix bidirectional replication synchronization issues'
\echo 'IMPORTANT: Follow the sequence instructions carefully!'
\echo ''

-- Step 1: Check current replication status before making changes
\echo 'STEP 1: CURRENT REPLICATION STATUS CHECK'
\echo '========================================'

SELECT 
    'Before Fix - Subscription Status' as check_type,
    subname,
    subenabled,
    CASE 
        WHEN subenabled THEN '✅ Enabled'
        ELSE '❌ Disabled'
    END as status
FROM pg_subscription;

SELECT 
    'Before Fix - Worker Status' as check_type,
    subname,
    CASE
        WHEN pid IS NULL THEN '❌ INACTIVE'
        ELSE '✅ ACTIVE'
    END as worker_status,
    latest_end_time
FROM pg_stat_subscription;

-- Step 2: Disable all subscriptions temporarily
\echo ''
\echo 'STEP 2: DISABLING ALL SUBSCRIPTIONS'
\echo '=================================='

-- Disable subscriptions to prevent conflicts during fix
DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        EXECUTE format('ALTER SUBSCRIPTION %I DISABLE', sub_record.subname);
        RAISE NOTICE 'Disabled subscription: %', sub_record.subname;
    END LOOP;
END $$;

-- Wait a moment for workers to stop
SELECT pg_sleep(2);

-- Step 3: Check for and resolve replication conflicts
\echo ''
\echo 'STEP 3: CHECKING FOR REPLICATION CONFLICTS'
\echo '========================================='

-- Check for duplicate data that might cause conflicts
SELECT 
    'Potential Conflicts' as check_type,
    'Duplicate Addresses' as conflict_type,
    address,
    COUNT(*) as duplicate_count
FROM inboxes 
GROUP BY address 
HAVING COUNT(*) > 1;

-- Check for inactive replication slots
SELECT 
    'Inactive Slots' as check_type,
    slot_name,
    active,
    restart_lsn
FROM pg_replication_slots 
WHERE NOT active;

-- Step 4: Clean up inactive replication slots
\echo ''
\echo 'STEP 4: CLEANING UP INACTIVE REPLICATION SLOTS'
\echo '============================================='

DO $$
DECLARE
    slot_record RECORD;
BEGIN
    FOR slot_record IN 
        SELECT slot_name FROM pg_replication_slots WHERE NOT active
    LOOP
        PERFORM pg_drop_replication_slot(slot_record.slot_name);
        RAISE NOTICE 'Dropped inactive replication slot: %', slot_record.slot_name;
    END LOOP;
END $$;

-- Step 5: Re-enable subscriptions with proper settings
\echo ''
\echo 'STEP 5: RE-ENABLING SUBSCRIPTIONS'
\echo '================================'

-- Re-enable subscriptions one by one
DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        -- Enable subscription
        EXECUTE format('ALTER SUBSCRIPTION %I ENABLE', sub_record.subname);
        RAISE NOTICE 'Enabled subscription: %', sub_record.subname;
        
        -- Wait a moment between enabling subscriptions
        PERFORM pg_sleep(1);
    END LOOP;
END $$;

-- Step 6: Refresh subscriptions to ensure proper sync
\echo ''
\echo 'STEP 6: REFRESHING SUBSCRIPTION PUBLICATIONS'
\echo '==========================================='

DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        BEGIN
            EXECUTE format('ALTER SUBSCRIPTION %I REFRESH PUBLICATION', sub_record.subname);
            RAISE NOTICE 'Refreshed publication for subscription: %', sub_record.subname;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not refresh subscription % (this may be normal): %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Step 7: Wait for initial synchronization
\echo ''
\echo 'STEP 7: WAITING FOR INITIAL SYNCHRONIZATION'
\echo '=========================================='

SELECT pg_sleep(5);

-- Step 8: Check subscription worker status after fix
\echo ''
\echo 'STEP 8: POST-FIX STATUS CHECK'
\echo '============================'

SELECT 
    'After Fix - Subscription Status' as check_type,
    subname,
    subenabled,
    CASE 
        WHEN subenabled THEN '✅ Enabled'
        ELSE '❌ Disabled'
    END as status
FROM pg_subscription;

SELECT 
    'After Fix - Worker Status' as check_type,
    subname,
    CASE
        WHEN pid IS NULL THEN '❌ INACTIVE (may need time to start)'
        ELSE '✅ ACTIVE'
    END as worker_status,
    latest_end_time,
    CASE 
        WHEN latest_end_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - latest_end_time))::INTEGER || ' seconds ago'
        ELSE 'Never'
    END as last_activity
FROM pg_stat_subscription;

-- Step 9: Check replication slot status
SELECT 
    'After Fix - Replication Slots' as check_type,
    slot_name,
    active,
    CASE
        WHEN active THEN '✅ ACTIVE'
        ELSE '❌ INACTIVE'
    END as status
FROM pg_replication_slots;

-- Step 10: Final data consistency check
\echo ''
\echo 'STEP 10: DATA CONSISTENCY CHECK'
\echo '=============================='

SELECT 
    'Current Inbox Count' as metric,
    COUNT(*) as total_inboxes,
    COUNT(*) FILTER (WHERE is_active = true) as active_inboxes,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') as recent_inboxes
FROM inboxes;

\echo ''
\echo '=== REPLICATION FIX COMPLETE ==='
\echo ''
\echo 'NEXT STEPS:'
\echo '1. Run this script on all three servers (EU, US, SG)'
\echo '2. Wait 5-10 minutes for full synchronization'
\echo '3. Test inbox creation on each server'
\echo '4. Verify all servers show the same inbox count'
\echo '5. Run the diagnosis script again to confirm fix'
\echo ''
\echo 'If issues persist, check PostgreSQL logs:'
\echo 'sudo tail -f /var/log/postgresql/postgresql-16-main.log'
