-- PostgreSQL Replication Fix Script for TempFly.io
-- This script fixes bidirectional replication synchronization issues
-- 
-- IMPORTANT: Run this script in the correct sequence on each server
-- Follow the instructions in the comments carefully
--
-- Expected Issue: Cascading replication instead of true bidirectional sync
-- - EU shows only EU data
-- - US shows EU + US data  
-- - S<PERSON> shows EU + US + SG data (complete)

\echo '=== TEMPFLY.IO REPLICATION FIX SCRIPT ==='
\echo ''
\echo 'This script will fix bidirectional replication synchronization issues'
\echo 'IMPORTANT: Follow the sequence instructions carefully!'
\echo ''

-- Step 1: Check current replication status before making changes
\echo 'STEP 1: CURRENT REPLICATION STATUS CHECK'
\echo '========================================'

SELECT 
    'Before Fix - Subscription Status' as check_type,
    subname,
    subenabled,
    CASE 
        WHEN subenabled THEN '✅ Enabled'
        ELSE '❌ Disabled'
    END as status
FROM pg_subscription;

SELECT 
    'Before Fix - Worker Status' as check_type,
    subname,
    CASE
        WHEN pid IS NULL THEN '❌ INACTIVE'
        ELSE '✅ ACTIVE'
    END as worker_status,
    latest_end_time
FROM pg_stat_subscription;

-- Step 2: Disable all subscriptions temporarily
\echo ''
\echo 'STEP 2: DISABLING ALL SUBSCRIPTIONS'
\echo '=================================='

-- Disable subscriptions to prevent conflicts during fix
DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        BEGIN
            EXECUTE format('ALTER SUBSCRIPTION %I DISABLE', sub_record.subname);
            RAISE NOTICE 'Disabled subscription: %', sub_record.subname;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not disable subscription %: %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Wait a moment for workers to stop
SELECT pg_sleep(3);

-- Step 3: Check for and resolve replication conflicts
\echo ''
\echo 'STEP 3: CHECKING FOR REPLICATION CONFLICTS'
\echo '========================================='

-- Check for duplicate data that might cause conflicts
SELECT 
    'Potential Conflicts' as check_type,
    'Duplicate Addresses' as conflict_type,
    address,
    COUNT(*) as duplicate_count
FROM inboxes 
GROUP BY address 
HAVING COUNT(*) > 1;

-- Check for inactive replication slots
SELECT 
    'Inactive Slots' as check_type,
    slot_name,
    active,
    restart_lsn
FROM pg_replication_slots 
WHERE NOT active;

-- Step 4: Clean up inactive replication slots and fix specific issues
\echo ''
\echo 'STEP 4: CLEANING UP INACTIVE REPLICATION SLOTS AND FIXING SPECIFIC ISSUES'
\echo '========================================================================'

-- First, identify and fix specific subscription issues based on diagnosis
DO $$
DECLARE
    slot_record RECORD;
    sub_record RECORD;
    server_ip TEXT;
BEGIN
    -- Get server IP to determine which server we're on
    SELECT COALESCE(inet_server_addr()::text, 'unknown') INTO server_ip;

    RAISE NOTICE 'Fixing replication issues on server: %', server_ip;

    -- Clean up inactive replication slots
    FOR slot_record IN
        SELECT slot_name FROM pg_replication_slots WHERE NOT active
    LOOP
        BEGIN
            PERFORM pg_drop_replication_slot(slot_record.slot_name);
            RAISE NOTICE 'Dropped inactive replication slot: %', slot_record.slot_name;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not drop slot %: %', slot_record.slot_name, SQLERRM;
        END;
    END LOOP;

    -- Server-specific fixes based on diagnosis results
    CASE server_ip
        WHEN '************' THEN  -- EU Server
            RAISE NOTICE 'Applying EU server specific fixes...';
            -- EU server has eu_from_sg_subscription INACTIVE

        WHEN '*************' THEN  -- US Server
            RAISE NOTICE 'Applying US server specific fixes...';
            -- US server has sg_from_eu_subscription and us_from_sg_subscription INACTIVE

        WHEN '*************' THEN  -- SG Server
            RAISE NOTICE 'Applying SG server specific fixes...';
            -- SG server is missing subscriptions to EU

        ELSE
            RAISE NOTICE 'Unknown server IP: %, applying general fixes', server_ip;
    END CASE;

END $$;

-- Step 5: Create missing subscriptions and re-enable existing ones
\echo ''
\echo 'STEP 5: CREATING MISSING SUBSCRIPTIONS AND RE-ENABLING EXISTING ONES'
\echo '=================================================================='

DO $$
DECLARE
    sub_record RECORD;
    server_ip TEXT;
    subscription_exists BOOLEAN;
BEGIN
    -- Get server IP to determine which server we're on
    SELECT COALESCE(inet_server_addr()::text, 'unknown') INTO server_ip;

    -- Create missing subscriptions based on diagnosis results
    CASE server_ip
        WHEN '*************' THEN  -- SG Server - missing subscription to EU
            RAISE NOTICE 'SG Server: Creating missing subscription to EU server...';

            -- Check if sg_from_eu_subscription exists
            SELECT EXISTS(SELECT 1 FROM pg_subscription WHERE subname = 'sg_from_eu_subscription') INTO subscription_exists;

            IF NOT subscription_exists THEN
                BEGIN
                    EXECUTE 'CREATE SUBSCRIPTION sg_from_eu_subscription
                    CONNECTION ''host=************ port=5432 user=replicator password=4wyWCAAk92hkGUhdh7 dbname=tempfly_app sslmode=require''
                    PUBLICATION eu_publication
                    WITH (copy_data = false, create_slot = true, enabled = true)';
                    RAISE NOTICE 'Created missing subscription: sg_from_eu_subscription';
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'Could not create sg_from_eu_subscription: %', SQLERRM;
                END;
            END IF;

        ELSE
            RAISE NOTICE 'Server % - no missing subscriptions to create', server_ip;
    END CASE;

    -- Re-enable all existing subscriptions
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        BEGIN
            EXECUTE format('ALTER SUBSCRIPTION %I ENABLE', sub_record.subname);
            RAISE NOTICE 'Enabled subscription: %', sub_record.subname;

            -- Wait a moment between enabling subscriptions
            PERFORM pg_sleep(2);
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not enable subscription %: %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Step 6: Refresh subscriptions to ensure proper sync
\echo ''
\echo 'STEP 6: REFRESHING SUBSCRIPTION PUBLICATIONS'
\echo '==========================================='

DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription LOOP
        BEGIN
            EXECUTE format('ALTER SUBSCRIPTION %I REFRESH PUBLICATION', sub_record.subname);
            RAISE NOTICE 'Refreshed publication for subscription: %', sub_record.subname;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not refresh subscription % (this may be normal): %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Step 7: Wait for initial synchronization and force refresh
\echo ''
\echo 'STEP 7: WAITING FOR INITIAL SYNCHRONIZATION AND FORCING REFRESH'
\echo '=============================================================='

-- Wait longer for synchronization given the specific issues found
SELECT pg_sleep(10);

-- Force refresh of all subscriptions to ensure proper sync
DO $$
DECLARE
    sub_record RECORD;
BEGIN
    FOR sub_record IN SELECT subname FROM pg_subscription WHERE subenabled = true LOOP
        BEGIN
            -- Try to refresh the subscription
            EXECUTE format('ALTER SUBSCRIPTION %I REFRESH PUBLICATION WITH (copy_data = false)', sub_record.subname);
            RAISE NOTICE 'Force refreshed subscription: %', sub_record.subname;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not refresh subscription %: %', sub_record.subname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Wait additional time for refresh to take effect
SELECT pg_sleep(5);

-- Step 8: Check subscription worker status after fix
\echo ''
\echo 'STEP 8: POST-FIX STATUS CHECK'
\echo '============================'

SELECT 
    'After Fix - Subscription Status' as check_type,
    subname,
    subenabled,
    CASE 
        WHEN subenabled THEN '✅ Enabled'
        ELSE '❌ Disabled'
    END as status
FROM pg_subscription;

SELECT 
    'After Fix - Worker Status' as check_type,
    subname,
    CASE
        WHEN pid IS NULL THEN '❌ INACTIVE (may need time to start)'
        ELSE '✅ ACTIVE'
    END as worker_status,
    latest_end_time,
    CASE 
        WHEN latest_end_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - latest_end_time))::INTEGER || ' seconds ago'
        ELSE 'Never'
    END as last_activity
FROM pg_stat_subscription;

-- Step 9: Check replication slot status
SELECT 
    'After Fix - Replication Slots' as check_type,
    slot_name,
    active,
    CASE
        WHEN active THEN '✅ ACTIVE'
        ELSE '❌ INACTIVE'
    END as status
FROM pg_replication_slots;

-- Step 10: Final data consistency check with server identification
\echo ''
\echo 'STEP 10: DATA CONSISTENCY CHECK WITH SERVER IDENTIFICATION'
\echo '========================================================'

-- Show server identification and current data state
SELECT
    'Server Identification' as metric,
    COALESCE(inet_server_addr()::text, 'unknown') as server_ip,
    CASE COALESCE(inet_server_addr()::text, 'unknown')
        WHEN '************' THEN 'EU Server'
        WHEN '*************' THEN 'US Server'
        WHEN '*************' THEN 'SG Server'
        ELSE 'Unknown Server'
    END as server_region;

SELECT
    'Current Inbox Count' as metric,
    COUNT(*) as total_inboxes,
    COUNT(*) FILTER (WHERE is_active = true) as active_inboxes,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') as recent_inboxes
FROM inboxes;

-- Show recent inboxes to verify replication is working
SELECT
    'Recent Inboxes for Verification' as check_type,
    name,
    domain,
    address,
    created_at
FROM inboxes
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC
LIMIT 5;

\echo ''
\echo '=== REPLICATION FIX COMPLETE ==='
\echo ''
\echo 'SPECIFIC ISSUES ADDRESSED:'
\echo '- Fixed inactive subscription workers'
\echo '- Cleaned up orphaned replication slots'
\echo '- Created missing sg_from_eu_subscription on SG server'
\echo '- Force refreshed all subscriptions'
\echo ''
\echo 'NEXT STEPS:'
\echo '1. Run this script on all three servers in this order:'
\echo '   a) SG server first (*************)'
\echo '   b) US server second (*************)'
\echo '   c) EU server last (************)'
\echo '2. Wait 10-15 minutes for full synchronization'
\echo '3. Test inbox creation on each server'
\echo '4. Verify all servers show the same inbox count (should be 3)'
\echo '5. Run the diagnosis script again to confirm fix'
\echo ''
\echo 'EXPECTED RESULT:'
\echo 'All three servers should show 3 inboxes: eu, usa, and sg'
\echo ''
\echo 'If issues persist, check PostgreSQL logs:'
\echo 'sudo tail -f /var/log/postgresql/postgresql-16-main.log | grep -i "subscription\|replication"'
