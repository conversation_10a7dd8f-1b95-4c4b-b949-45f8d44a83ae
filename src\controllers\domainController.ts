import { Request, Response, NextFunction } from 'express';
import { DomainModel } from '../models/Domain';
import { v4 as uuidv4 } from 'uuid';
import { getCache, setCache, isRedisConnected, redisClient } from '../config/redis-direct';
import logger from '../utils/logger';
import localCache from '../utils/localCache';
import { getMemoryCache, setMemoryCache } from '../utils/memoryCache';
import { AppError } from '../middlewares/errorHandler';

/**
 * Cache key for domains list
 */
const DOMAINS_CACHE_KEY = 'domains:all';

/**
 * Cache TTL for domains list (7 days since domains rarely change)
 */
const DOMAINS_CACHE_TTL = 604800; // 7 days

// PHASE 2: Optimized domain preloading with reduced frequency
let preloadedDomains: any[] = [];
let preloadedDomainsTimestamp = 0;
const PRELOAD_REFRESH_INTERVAL = 3600000; // 1 hour (reduced to avoid timeout overflow warning)

// Function to preload domains
async function preloadDomains() {
  try {
    logger.info('Preloading domains from database...');
    const startTime = Date.now();

    // If we already have domains in memory and they're not too old, use them
    if (preloadedDomains.length > 0 &&
        (Date.now() - preloadedDomainsTimestamp) < PRELOAD_REFRESH_INTERVAL / 2) {
      logger.info(`Using existing preloaded domains (${preloadedDomains.length}) to avoid database load`);
      return;
    }

    // Try to get domains from database with retry logic
    let domains: any[] = [];
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        domains = await DomainModel.getAll();
        break; // Success, exit the retry loop
      } catch (dbError: unknown) {
        retryCount++;
        logger.warn(`Database error when preloading domains (attempt ${retryCount}/${maxRetries}):`,
          dbError instanceof Error ? dbError.message : String(dbError));

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);
          logger.info(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // If we have preloaded domains, keep using them even if they're old
          if (preloadedDomains.length > 0) {
            logger.warn(`Using existing ${preloadedDomains.length} domains after ${maxRetries} failed attempts to refresh`);
            return;
          }
          // Otherwise, re-throw the error
          throw dbError;
        }
      }
    }

    const dbTime = Date.now() - startTime;

    if (domains.length > 0) {
      preloadedDomains = domains;
      preloadedDomainsTimestamp = Date.now();

      logger.info(`Preloaded ${domains.length} domains in ${dbTime}ms`);

      // Update cache
      localCache.set(DOMAINS_CACHE_KEY, domains, DOMAINS_CACHE_TTL);

      if (isRedisConnected()) {
        await setCache(DOMAINS_CACHE_KEY, domains, DOMAINS_CACHE_TTL);
      }
    } else {
      logger.warn('No domains returned from database');
    }
  } catch (error) {
    logger.error('Failed to preload domains:', error instanceof Error ? error : new Error(String(error)));

    // If we have no domains at all, create a fallback domain for emergency use
    if (preloadedDomains.length === 0) {
      logger.warn('Creating fallback domain for emergency use');
      preloadedDomains = [{ domain: 'tempfly.io', active: true }];
      preloadedDomainsTimestamp = Date.now();
    }
  }
}

// Use Redis to implement a simple leader election
async function tryBecomeLeader() {
  if (isRedisConnected()) {
    const instanceId = process.env.INSTANCE_ID || 'unknown';
    try {
      const result = await redisClient.set('domain-preload-leader', instanceId, {
        NX: true,
        EX: 3700 // Slightly longer than the refresh interval
      });
      return result === 'OK';
    } catch (error) {
      logger.error('Error in leader election:', error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }
  return false;
}

// Only preload if this instance is the leader
async function leaderPreloadDomains() {
  const isLeader = await tryBecomeLeader();
  if (isLeader) {
    logger.info(`Instance ${process.env.INSTANCE_ID} is the domain preload leader`);
    await preloadDomains();
  } else {
    logger.debug(`Instance ${process.env.INSTANCE_ID} is not the domain preload leader, skipping preload`);
  }
}

// PHASE 2: Enhanced staggered startup with longer delays to reduce load spikes
const startupDelay = parseInt(process.env.INSTANCE_ID || '1') * 10000; // Increased from 5000ms to 10000ms
setTimeout(() => {
  logger.info(`PHASE 2: Starting domain preload leader election after ${startupDelay}ms delay (Instance ${process.env.INSTANCE_ID || 'unknown'})`);
  leaderPreloadDomains();
  setInterval(leaderPreloadDomains, PRELOAD_REFRESH_INTERVAL);
}, startupDelay);

/**
 * Get all available domains - Ultra-optimized version
 */
export const getDomains = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Use the request ID from middleware or generate a new one
  const requestId = (req as any).requestId || uuidv4().toUpperCase();

  // Wrap in async IIFE to use async/await while maintaining Express compatibility
  (async () => {
    try {
      // ULTRA-FAST PATH: Use preloaded domains if available
      // This is the fastest possible path with no logging or additional checks
      if (preloadedDomains.length > 0) {
        // Format the response directly without any intermediate processing
        const response = {
          'request-id': requestId,
          'message': 'Success',
          'data': preloadedDomains.map((domain: { domain: string }) => ({ domain: domain.domain })),
          'code': 0
        };

        // Send response
        res.status(200).json(response);
        return;
      }

      // FAST PATH: Try memory cache
      const memCacheDomains = getMemoryCache<any[]>(DOMAINS_CACHE_KEY);
      if (memCacheDomains && memCacheDomains.length > 0) {
        // Update preloaded domains for future requests
        preloadedDomains = memCacheDomains;
        preloadedDomainsTimestamp = Date.now();

        // Format and send response
        const response = {
          'request-id': requestId,
          'message': 'Success',
          'data': memCacheDomains.map((domain: { domain: string }) => ({ domain: domain.domain })),
          'code': 0
        };

        res.status(200).json(response);
        return;
      }

      // MEDIUM PATH: Try local cache
      const localCacheDomains = localCache.get<any[]>(DOMAINS_CACHE_KEY);
      if (localCacheDomains) {
        // Update preloaded domains and memory cache for future requests
        preloadedDomains = localCacheDomains;
        preloadedDomainsTimestamp = Date.now();
        setMemoryCache(DOMAINS_CACHE_KEY, localCacheDomains, DOMAINS_CACHE_TTL);

        // Format and send response
        const response = {
          'request-id': requestId,
          'message': 'Success',
          'data': localCacheDomains.map((domain: { domain: string }) => ({ domain: domain.domain })),
          'code': 0
        };

        res.status(200).json(response);
        return;
      }

      // SLOW PATH: Get from database and update all caches
      try {
        const domains = await DomainModel.getAll();

        // Only update caches if we got domains
        if (domains && domains.length > 0) {
          // Update all cache layers
          preloadedDomains = domains;
          preloadedDomainsTimestamp = Date.now();
          setMemoryCache(DOMAINS_CACHE_KEY, domains, DOMAINS_CACHE_TTL);
          localCache.set(DOMAINS_CACHE_KEY, domains, DOMAINS_CACHE_TTL);

          // Update Redis in the background without waiting
          if (isRedisConnected()) {
            setCache(DOMAINS_CACHE_KEY, domains, DOMAINS_CACHE_TTL).catch(() => {});
          }
        }

        // Format and send response
        const response = {
          'request-id': requestId,
          'message': 'Success',
          'data': domains.map((domain: { domain: string }) => ({ domain: domain.domain })),
          'code': 0
        };

        res.status(200).json(response);
        return;
      } catch (dbError) {
        // FALLBACK PATH: Use a fallback domain
        const fallbackDomains = [{ domain: 'tempfly.io', active: true }];

        // Update caches with fallback
        preloadedDomains = fallbackDomains;
        preloadedDomainsTimestamp = Date.now();
        setMemoryCache(DOMAINS_CACHE_KEY, fallbackDomains, 60);
        localCache.set(DOMAINS_CACHE_KEY, fallbackDomains, 60);

        // Format and send response
        const response = {
          'request-id': requestId,
          'message': 'Success',
          'data': fallbackDomains.map((domain: { domain: string }) => ({ domain: domain.domain })),
          'code': 0
        };

        res.status(200).json(response);
        return;
      }
    } catch (error) {
      next(new AppError('Failed to get domains', 500));
    }
  })();
};
