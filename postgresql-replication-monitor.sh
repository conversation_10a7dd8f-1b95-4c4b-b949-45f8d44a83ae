#!/bin/bash

# PostgreSQL Replication Monitoring Script for TempFly.io
# This script monitors replication health and sends alerts when issues are detected
# 
# Usage: ./postgresql-replication-monitor.sh
# Setup: Add to crontab to run every 5 minutes
# */5 * * * * /path/to/postgresql-replication-monitor.sh

# Configuration
LOG_FILE="/var/log/postgresql/replication_monitor.log"
ALERT_LOG="/var/log/postgresql/replication_alerts.log"
DATABASE="tempfly_app"
POSTGRES_USER="postgres"

# Thresholds
MAX_LAG_SECONDS=300  # 5 minutes
MAX_INACTIVE_TIME=600  # 10 minutes

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to log alerts
log_alert() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - ALERT: $1" | tee -a "$ALERT_LOG"
    echo -e "${RED}ALERT: $1${NC}"
}

# Function to log warnings
log_warning() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - WARNING: $1" | tee -a "$LOG_FILE"
    echo -e "${YELLOW}WARNING: $1${NC}"
}

# Function to log success
log_success() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - SUCCESS: $1" | tee -a "$LOG_FILE"
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

# Function to check if PostgreSQL is running
check_postgresql_status() {
    if ! systemctl is-active --quiet postgresql; then
        log_alert "PostgreSQL service is not running!"
        return 1
    fi
    return 0
}

# Function to check subscription status
check_subscription_status() {
    local inactive_count
    local total_count
    
    # Get subscription counts
    inactive_count=$(sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -t -c "
        SELECT COUNT(*) FROM pg_stat_subscription WHERE pid IS NULL;
    " 2>/dev/null | tr -d ' ')
    
    total_count=$(sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -t -c "
        SELECT COUNT(*) FROM pg_subscription;
    " 2>/dev/null | tr -d ' ')
    
    if [[ -z "$inactive_count" ]] || [[ -z "$total_count" ]]; then
        log_alert "Failed to query subscription status"
        return 1
    fi
    
    if [[ "$inactive_count" -gt 0 ]]; then
        log_alert "$inactive_count out of $total_count subscriptions are inactive"
        
        # Get details of inactive subscriptions
        sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -c "
            SELECT 
                subname as subscription_name,
                'INACTIVE' as status,
                latest_end_time
            FROM pg_stat_subscription 
            WHERE pid IS NULL;
        " 2>/dev/null | tee -a "$LOG_FILE"
        
        return 1
    else
        log_success "All $total_count subscriptions are active"
        return 0
    fi
}

# Function to check replication lag
check_replication_lag() {
    local high_lag_count=0
    
    # Check for high lag subscriptions
    while IFS='|' read -r subname lag_seconds; do
        if [[ -n "$subname" ]] && [[ -n "$lag_seconds" ]]; then
            if [[ "$lag_seconds" -gt "$MAX_LAG_SECONDS" ]]; then
                log_warning "High replication lag detected: $subname ($lag_seconds seconds)"
                ((high_lag_count++))
            fi
        fi
    done < <(sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -t -c "
        SELECT 
            subname || '|' || COALESCE(EXTRACT(EPOCH FROM (NOW() - latest_end_time))::INTEGER, 999999)
        FROM pg_stat_subscription 
        WHERE pid IS NOT NULL;
    " 2>/dev/null)
    
    if [[ "$high_lag_count" -gt 0 ]]; then
        log_alert "$high_lag_count subscriptions have high replication lag (>$MAX_LAG_SECONDS seconds)"
        return 1
    else
        log_success "All subscriptions have acceptable replication lag"
        return 0
    fi
}

# Function to check replication slots
check_replication_slots() {
    local inactive_slots
    
    inactive_slots=$(sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -t -c "
        SELECT COUNT(*) FROM pg_replication_slots WHERE NOT active;
    " 2>/dev/null | tr -d ' ')
    
    if [[ -z "$inactive_slots" ]]; then
        log_alert "Failed to query replication slots"
        return 1
    fi
    
    if [[ "$inactive_slots" -gt 0 ]]; then
        log_warning "$inactive_slots inactive replication slots found"
        
        # Show inactive slots
        sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -c "
            SELECT slot_name, plugin, slot_type, database, active 
            FROM pg_replication_slots 
            WHERE NOT active;
        " 2>/dev/null | tee -a "$LOG_FILE"
        
        return 1
    else
        log_success "All replication slots are active"
        return 0
    fi
}

# Function to check data consistency
check_data_consistency() {
    local inbox_count
    local server_ip
    
    # Get current server IP and inbox count
    server_info=$(sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -t -c "
        SELECT 
            COALESCE(inet_server_addr()::text, 'unknown') || '|' || 
            COUNT(*)::text
        FROM inboxes 
        WHERE is_active = true;
    " 2>/dev/null)
    
    if [[ -z "$server_info" ]]; then
        log_alert "Failed to query inbox data"
        return 1
    fi
    
    server_ip=$(echo "$server_info" | cut -d'|' -f1 | tr -d ' ')
    inbox_count=$(echo "$server_info" | cut -d'|' -f2 | tr -d ' ')
    
    # Determine server region
    case "$server_ip" in
        "************") server_region="EU" ;;
        "*************") server_region="US" ;;
        "*************") server_region="SG" ;;
        *) server_region="UNKNOWN" ;;
    esac
    
    log_message "Server: $server_region ($server_ip) - Active inboxes: $inbox_count"
    
    # Store count in temporary file for comparison (if running on multiple servers)
    echo "$server_region:$inbox_count:$(date +%s)" >> "/tmp/replication_counts.tmp"
    
    return 0
}

# Function to check for recent replication activity
check_recent_activity() {
    local recent_activity
    
    recent_activity=$(sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -t -c "
        SELECT COUNT(*) 
        FROM pg_stat_subscription 
        WHERE latest_end_time > NOW() - INTERVAL '$MAX_INACTIVE_TIME seconds';
    " 2>/dev/null | tr -d ' ')
    
    if [[ -z "$recent_activity" ]] || [[ "$recent_activity" -eq 0 ]]; then
        log_alert "No recent replication activity detected (last $MAX_INACTIVE_TIME seconds)"
        return 1
    else
        log_success "Recent replication activity detected"
        return 0
    fi
}

# Function to generate summary report
generate_summary() {
    log_message "=== REPLICATION HEALTH SUMMARY ==="
    
    # Get subscription summary
    sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -c "
        SELECT 
            'Subscription Summary' as report_type,
            subname,
            CASE WHEN pid IS NULL THEN '❌ INACTIVE' ELSE '✅ ACTIVE' END as status,
            CASE WHEN latest_end_time IS NOT NULL THEN 
                EXTRACT(EPOCH FROM (NOW() - latest_end_time))::INTEGER || 's ago'
            ELSE 'Never' END as last_sync
        FROM pg_stat_subscription
        ORDER BY subname;
    " 2>/dev/null | tee -a "$LOG_FILE"
    
    # Get replication slot summary
    sudo -u "$POSTGRES_USER" psql -d "$DATABASE" -c "
        SELECT 
            'Replication Slots' as report_type,
            slot_name,
            CASE WHEN active THEN '✅ ACTIVE' ELSE '❌ INACTIVE' END as status,
            plugin
        FROM pg_replication_slots
        ORDER BY slot_name;
    " 2>/dev/null | tee -a "$LOG_FILE"
    
    log_message "=== END SUMMARY ==="
}

# Main monitoring function
main() {
    log_message "Starting replication health check"
    
    local exit_code=0
    
    # Check PostgreSQL status
    if ! check_postgresql_status; then
        exit_code=1
    fi
    
    # Check subscription status
    if ! check_subscription_status; then
        exit_code=1
    fi
    
    # Check replication lag
    if ! check_replication_lag; then
        exit_code=1
    fi
    
    # Check replication slots
    if ! check_replication_slots; then
        exit_code=1
    fi
    
    # Check data consistency
    if ! check_data_consistency; then
        exit_code=1
    fi
    
    # Check recent activity
    if ! check_recent_activity; then
        exit_code=1
    fi
    
    # Generate summary
    generate_summary
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "All replication health checks passed"
    else
        log_alert "One or more replication health checks failed"
    fi
    
    log_message "Replication health check completed"
    echo "----------------------------------------" >> "$LOG_FILE"
    
    return $exit_code
}

# Create log directories if they don't exist
mkdir -p "$(dirname "$LOG_FILE")"
mkdir -p "$(dirname "$ALERT_LOG")"

# Run main function
main "$@"
